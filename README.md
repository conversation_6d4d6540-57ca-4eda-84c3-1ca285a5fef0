# 自动化生图工具

这是一个基于Flask和现代Web技术构建的自动化图片生成工具，支持批量处理Excel文件中的分镜数据，调用OpenAI兼容的图片生成API来自动生成图片。

## ✨ 主要功能

### 1. 通用模型服务配置
- 支持添加多个OpenAI兼容的模型服务
- 自定义API密钥、接口地址
- 配置超时时间（默认3分钟）
- 设置重试次数（默认3次）
- 支持对话模型和生图模型分类管理
- **自动选择**: 默认选择第一个可用的生图模型

### 2. 角色参考图配置
- 配置多个角色，每个角色包含唯一编码、角色名、角色URL
- **角色核心特征**: 支持为角色配置不可变的核心特征描述
- 角色图片预览功能，点击可放大查看
- 在生图时可选择最多5个角色作为参考图
- **智能参考图说明**: 自动生成包含核心特征的参考图说明

### 3. GitHub图床功能
- **GitHub集成**: 支持上传图片到GitHub仓库作为图床
- **配置简单**: 只需配置GitHub Token和仓库路径
- **文件大小限制**: 可配置最大上传文件大小（默认3MB）
- **自动命名**: 上传的图片自动按时间戳命名
- **拖拽上传**: 支持拖拽文件到上传区域
- **即时预览**: 上传成功后立即显示图片和链接
- **一键复制**: 快速复制图片链接到剪贴板

### 4. 配置持久化
- 所有配置自动保存到本地JSON文件
- 支持配置的导入和导出功能
- **数据缓存**: 页面刷新后自动恢复数据状态
- **手动保存缓存**: 提供手动保存按钮，确保数据安全

### 5. 角色形象生成功能
- **多提示词管理**: 支持添加多个角色生成提示词
- **参考角色选择**: 为每个角色选择参考图，自动生成参考图说明
- **并发生图**: 支持多个角色同时生成，提高效率
- **时间戳命名**: 生成的图片按时间戳自动命名
- **MD5去重**: 自动检测并去除重复的图片
- **数据持久化**: 自动保存和恢复角色生成数据
- **批量操作**: 一键生成所有启用的角色图片
- **进度跟踪**: 实时显示生成进度和状态
- **图片回显**: 生成完成后立即显示在角色卡片中
- **图片导航**: 支持上一张/下一张按钮浏览多张图片
- **模态框预览**: 点击图片可放大查看，支持键盘导航

### 6. 一键生成故事功能
- **Excel文件导入**: 读取包含"镜头序号"和"文生图prompt"列的Excel文件
- **分镜管理**: 每个分镜可以单独配置、启用/禁用
- **批量操作**: 统一设置所有分镜的生图数量
- **参考角色选择**: 为每个分镜选择参考角色，自动生成参考图说明
- **单个生图**: 可以单独为某个分镜生成图片
- **批量生图**: 一键生成所有启用的分镜
- **图片去重**: 自动检测并去除重复的图片
- **智能命名**: 图片按格式命名：`Excel文件名_镜头序号_序号.png`
- **Excel导出**: 导出包含分镜数据、提示词、参考图说明和角色URL的Excel文件

### 7. 图片管理功能
- **图片预览**: 生成的图片自动显示在分镜卡片中
- **图片导航**: 支持上一张/下一张图片浏览
- **图片放大**: 点击图片可在模态框中放大查看
- **图片信息**: 显示图片尺寸、文件大小等详细信息
- **批量查看**: 支持查看所有生成的图片

### 8. 进度跟踪与日志
- 实时显示批量生成的进度条
- 预估完成时间
- 完整的操作日志记录
- 支持按TraceID跟踪并发操作
- 详细的API调用日志（入参、出参、耗时）

### 9. 现代化UI界面
- 响应式设计，支持桌面和移动设备
- 标签页式界面，功能模块清晰分离
- 实时日志面板
- 图片预览功能
- 优雅的加载动画和进度指示
- **优化布局**: 参考图说明框与分镜提示词框宽度一致，高度更紧凑

## 🏗️ 技术架构

### 后端技术栈
- **Flask**: Web框架
- **Python 3.8+**: 核心语言
- **pandas**: Excel文件处理
- **openpyxl**: Excel文件读写
- **requests**: HTTP客户端
- **模块化设计**:
  - `config_manager`: 配置管理
  - `model_service`: 模型服务调用
  - `character_manager`: 角色管理（支持核心特征）
  - `excel_processor`: Excel处理（支持导出）
  - `image_generator`: 图片生成
  - `logger`: 日志系统

### 前端技术栈
- **原生JavaScript**: 无框架依赖，性能优异
- **CSS Grid/Flexbox**: 现代布局
- **Font Awesome**: 图标库
- **响应式设计**: 适配各种屏幕尺寸
- **LocalStorage**: 数据持久化
- **模态框**: 图片预览和放大功能

## 安装和使用

### 1. 系统要求
- Windows 10/11
- Python 3.8 或更高版本
- 4GB+ 内存

### 2. 快速启动
双击运行 `start.bat` 文件，脚本会自动：
1. 检查Python环境
2. 创建虚拟环境
3. 安装依赖包
4. 启动Web服务器

### 3. 手动安装
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 启动应用
python app.py
```

### 4. 访问应用
打开浏览器访问: http://localhost:5000

## 📖 使用流程

### 1. 配置模型服务
1. 点击"模型服务配置"标签页
2. 添加你的OpenAI兼容服务：
   - 服务名称: 自定义名称
   - API地址: 如 `https://api.openai.com/v1/chat/completions`
   - API密钥: 你的API密钥
   - 配置超时时间和重试次数
3. 系统会自动选择第一个可用的生图模型

### 2. 配置角色参考图
1. 点击"角色参考图配置"标签页
2. 添加角色：
   - 角色名称: 如"卡布奇诺咖啡女"
   - 角色编码: 唯一标识，如"cappuccino_girl"
   - 角色URL: 参考图片的在线地址
   - **角色核心特征**: 描述角色的不可变特征（可选）
     - 例如："她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。"

### 3. 配置GitHub图床（可选）
1. 点击"图床"标签页
2. 配置GitHub图床：
   - **GitHub API Token**: 在GitHub设置中生成Personal Access Token（需要repo权限）
   - **仓库路径**: 格式为`/username/repo/contents/path`，默认`/scys-wcs/photo/contents/image`
   - **最大文件大小**: 设置上传文件大小限制（默认3MB）
3. 点击"保存配置"并测试连接
4. 上传图片：
   - 点击上传区域选择图片或直接拖拽图片
   - 支持JPG、PNG、GIF格式
   - 上传成功后获得图片链接，可用于角色配置

### 4. 准备Excel文件
创建包含以下列的Excel文件：
- `镜头序号`: 分镜编号
- `文生图prompt`: 图片生成的描述文本

### 5. 角色形象生成（可选）
1. 点击"角色形象生成"标签页
2. 配置生成参数：
   - 选择模型服务和生图模型（会自动选择第一个）
   - 设置生图数量（每个提示词生成的图片数量）
   - 设置保存目录
3. 添加角色提示词：
   - 点击"添加角色提示词"
   - 输入角色生成的描述文本
   - 选择参考角色（可选，最多5个）
   - 系统会自动生成参考图说明
4. 生成角色图片：
   - 点击单个"生成图片"按钮生成单个角色
   - 点击"开始生图"批量生成所有启用的角色
   - 支持并发生成，提高效率
5. 管理生成结果：
   - 查看生成的角色图片
   - 使用"保存缓存"保存当前数据

### 6. 生成图片
1. 在"一键生成故事"页面选择Excel文件
2. 选择模型服务和生图模型（会自动选择第一个）
3. 设置保存目录
4. 为每个分镜选择参考角色（可选）
   - 选择后会自动生成包含核心特征的参考图说明
5. 调整生图数量
6. 点击"保存缓存"确保数据安全
7. 点击"一键生成所有分镜"或单独生成

### 7. 管理生成的图片
1. 生成完成后，图片会显示在对应分镜卡片中
2. 使用导航按钮浏览多张图片
3. 点击图片可放大查看详细信息
4. 使用"导出Excel文件"保存当前配置

## 🚀 核心功能详解

### 角色核心特征系统
- **功能**: 为角色配置不可变的核心特征描述
- **应用**: 自动在参考图说明中添加核心特征
- **格式**: `角色名的角色形象:请严格参考我提供的第X张图片(角色参考图)来塑造角色。角色不可变的核心特征=【具体特征描述】`
- **优势**: 确保生成图片中角色特征的一致性

### 数据持久化系统
- **自动保存**: 关键操作后自动保存数据到浏览器本地存储
- **手动保存**: 提供"保存缓存"按钮，主动保存并显示详细统计
- **数据恢复**: 页面刷新后自动恢复所有数据状态
- **完整性**: 保存Excel文件信息、分镜数据、参考角色选择、生成图片等

### Excel导出功能
- **导出内容**:
  - 分镜号
  - 文生图提示词（用户修改后的）
  - 参考角色URL（多个URL用换行分隔）
  - 参考图说明（包含核心特征）
- **格式**: .xlsx格式，支持自动列宽和文本换行
- **编码**: 正确处理中文和特殊字符

### 角色形象生成系统
- **多提示词管理**: 支持添加、编辑、删除多个角色提示词
- **参考角色集成**: 与角色参考图配置无缝集成
- **并发生成**: 多个角色同时生成，提高效率
- **时间戳命名**: 格式为`character_YYYYMMDD_HHMMSS_N.png`
- **数据持久化**: 自动保存到localStorage，页面刷新后恢复
- **进度跟踪**: 实时显示生成进度和预估时间
- **MD5去重**: 自动检测并去除重复图片

### GitHub图床系统
- **GitHub集成**: 直接上传图片到GitHub仓库
- **配置管理**: 支持GitHub Token和仓库路径配置
- **文件验证**: 自动验证文件类型和大小
- **自动命名**: 按时间戳自动生成文件名
- **即时反馈**: 上传进度显示和结果预览
- **链接生成**: 自动生成可访问的图片链接
- **格式**: `https://raw.githubusercontent.com/username/repo/main/path/filename.ext`

### 图片管理系统
- **预览**: 生成的图片自动显示在分镜卡片中
- **导航**: 支持上一张/下一张图片浏览
- **放大**: 点击图片在模态框中放大查看
- **信息**: 显示图片尺寸、文件大小等详细信息

## 🔌 API接口

### 图片生成接口
- **含参考图**: 自动构建包含角色参考图的请求
- **不含参考图**: 纯文本提示的图片生成
- **自动重试**: 失败时自动重试指定次数
- **超时控制**: 可配置的请求超时时间

### 支持的图片格式
- OpenAI格式: `https://videos.openai.com/...`
- 通用CDN格式: `https://filesystem.site/cdn/...`
- 其他HTTP/HTTPS图片链接

## ⚙️ 配置文件

### config.json
```json
{
  "model_services": [
    {
      "id": 1,
      "name": "OpenAI GPT-4",
      "api_url": "https://api.openai.com/v1/chat/completions",
      "api_key": "your-api-key",
      "timeout": 180,
      "retry_count": 3,
      "models": [
        {"name": "dall-e-3", "type": "image"}
      ]
    }
  ],
  "characters": [
    {
      "id": "1",
      "name": "卡布奇诺咖啡女",
      "code": "cappuccino_girl",
      "url": "https://example.com/character.jpg",
      "display_type": "name",
      "core_features": "她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。"
    }
  ],
  "imagehost": {
    "github_token": "your-github-token",
    "repo_path": "/scys-wcs/photo/contents/image",
    "max_file_size": 3
  },
  "settings": {
    "save_directory": "D:\\BaiduSyncdisk\\Youtube\\wcs_util",
    "timeout": 180,
    "retry_count": 3,
    "default_service": "1"
  }
}
```

### 字段说明
- **characters.display_type**: 显示类型，"name"显示角色名，"code"显示角色编码
- **characters.core_features**: 角色核心特征描述（可选）
- **settings.default_service**: 默认模型服务ID
- **imagehost.github_token**: GitHub Personal Access Token（需要repo权限）
- **imagehost.repo_path**: GitHub仓库路径，格式：/username/repo/contents/path
- **imagehost.max_file_size**: 最大上传文件大小（MB）

## 🔧 故障排除

### 常见问题

1. **无法启动应用**
   - 检查Python版本是否为3.8+
   - 确保所有依赖都已正确安装
   - 检查端口5000是否被占用

2. **Excel文件无法读取**
   - 确保文件格式为.xlsx或.xls
   - 检查是否包含必需的列："镜头序号"和"文生图prompt"
   - 确保文件没有被其他程序占用

3. **Excel导出失败**
   - 检查是否安装了openpyxl库：`pip install openpyxl`
   - 确保有写入权限到exports目录
   - 检查数据是否包含特殊字符导致编码问题

4. **API调用失败**
   - 检查网络连接
   - 验证API密钥是否正确
   - 确认API地址格式正确
   - 检查模型服务是否正常运行

5. **图片无法保存**
   - 检查保存目录是否存在且有写入权限
   - 确保磁盘空间充足
   - 检查图片URL是否可访问

6. **数据持久化问题**
   - 检查浏览器是否支持localStorage
   - 清除浏览器缓存后重试
   - 使用"保存缓存"按钮手动保存数据
   - 检查数据大小是否超过localStorage限制（通常5-10MB）

7. **角色参考图无法显示**
   - 检查角色URL是否正确
   - 确认图片链接可以直接访问
   - 检查是否存在跨域问题

8. **图床上传失败**
   - 检查GitHub Token是否正确且有repo权限
   - 确认仓库路径格式正确：`/username/repo/contents/path`
   - 检查文件大小是否超过限制
   - 确认网络连接正常
   - 验证GitHub仓库是否存在且可访问

9. **图床配置问题**
   - GitHub Token需要在GitHub设置中生成Personal Access Token
   - Token需要勾选repo权限
   - 仓库必须是公开的或者Token有访问权限
   - 路径中的contents是必需的，不能省略

### 日志查看
- 页面右侧有实时日志面板
- 详细日志保存在 `log.txt` 文件中
- API调用日志单独保存，包含traceID
- 使用"保存缓存"功能可查看详细的数据统计

## 🔄 更新日志

### v1.9.3 (2025-07-28)
**角色形象生成界面优化**:
- ✅ 角色选择支持缩略图显示，与一键生成故事模块一致
- ✅ 参考图说明移至角色提示词下方，布局更合理
- ✅ 保存目录与一键生成故事模块同步，支持浏览按钮
- ✅ 网格布局角色选择，悬停效果和选中状态
- ✅ 完善的用户交互体验和视觉反馈

**界面改进**:
- ✅ 角色缩略图网格布局（180px卡片）
- ✅ 目录选择浏览按钮和同步机制
- ✅ 布局逻辑优化和响应式设计
- ✅ 模块间配置一致性保证

### v1.9.2 (2025-07-28)
**角色形象生成图片功能完善**:
- ✅ 完整的图片回显功能，生成后立即显示
- ✅ 图片导航系统：上一张/下一张按钮
- ✅ 图片计数显示（如：2 / 5）
- ✅ 模态框预览：点击图片放大查看
- ✅ 键盘导航：支持左右箭头键和ESC键
- ✅ 与一键生成故事模块相同的浏览体验

**技术改进**:
- ✅ 图片导航状态管理
- ✅ 模态框上下文切换
- ✅ 响应式图片展示
- ✅ 交互反馈优化

### v1.9.1 (2025-07-28)
**角色形象生成模块优化**:
- ✅ 优化角色提示词输入框大小（120px高度）
- ✅ 优化参考图说明框大小和位置（与提示词框同样大小）
- ✅ 修复生成数量显示和编辑功能
- ✅ 新增"应用到所有"按钮，统一设置所有角色的生图数量
- ✅ 修复API端点错误，改进错误处理

**界面改进**:
- ✅ 更好的布局和视觉层次
- ✅ 响应式设计优化
- ✅ 改进的用户交互体验

### v1.9.0 (2025-07-28)
**新增功能**:
- ✅ 角色形象生成功能模块
- ✅ 多提示词管理和并发生成
- ✅ 时间戳命名和MD5去重
- ✅ 角色生成数据持久化

**用户体验改进**:
- ✅ 专门的角色生成工作流
- ✅ 实时进度跟踪和状态显示
- ✅ 与现有角色参考图系统无缝集成

### v1.8.0 (2025-07-28)
**新增功能**:
- ✅ GitHub图床功能模块
- ✅ 支持拖拽上传图片
- ✅ 自动文件大小验证和命名
- ✅ 即时预览和链接复制功能

**技术改进**:
- ✅ 新增ImageHostManager模块
- ✅ GitHub API集成
- ✅ 完善的错误处理和用户反馈

### v1.7.0 (2025-07-28)
**新增功能**:
- ✅ 手动"保存缓存"按钮，提供主动数据备份能力
- ✅ 详细的保存统计信息显示
- ✅ 完善的按钮状态管理

**用户体验改进**:
- ✅ 提供主动数据备份能力
- ✅ 增强数据持久化的可见性和可控性
- ✅ 便于故障排查和数据验证

### v1.6.0 (2025-07-28)
**问题修复**:
- ✅ 修复Excel导出0kb问题
- ✅ 改进JSON请求处理和错误处理
- ✅ 增强文件验证和日志记录

### v1.5.0 (2025-07-28)
**UI改进**:
- ✅ 参考图说明框样式优化（宽度100%，高度60-120px）
- ✅ 生图模型自动选择第一个可用模型
- ✅ 改进了视觉层次和用户体验

### v1.4.0 (2025-07-28)
**新增功能**:
- ✅ 角色配置新增"角色核心特征"字段
- ✅ 参考图说明自动包含角色核心特征
- ✅ 支持多行文本输入和特殊字符处理

### v1.3.0 (2025-07-28)
**新增功能**:
- ✅ 图片预览和导航功能
- ✅ 图片放大查看模态框
- ✅ 图片详细信息显示
- ✅ 批量图片管理

### v1.2.0 (2025-07-28)
**新增功能**:
- ✅ Excel导出功能
- ✅ 数据持久化系统
- ✅ 自动数据恢复

### v1.1.0 (2025-07-28)
**基础功能**:
- ✅ 模型服务配置
- ✅ 角色参考图配置
- ✅ 一键生成故事功能
- ✅ 批量图片生成

## 🚀 扩展性

本工具采用模块化设计，便于后续扩展：
- 支持添加新的图片生成服务
- 可扩展更多的文件格式支持
- 易于集成其他AI服务
- 支持插件式功能模块
- 完善的数据持久化系统
- 灵活的角色特征配置

## 📄 许可证

本项目仅供学习和个人使用。请遵守相关API服务的使用条款。

---

**当前版本**: v1.9.3
**最后更新**: 2025-07-28
**开发状态**: 活跃开发中