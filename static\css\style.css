/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    margin-top: 20px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 30px;
}

.header h1 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
}

.header h1 i {
    color: #667eea;
    margin-right: 10px;
}

.header-controls {
    display: flex;
    gap: 10px;
}

/* 标签页样式 */
.nav-tabs {
    display: flex;
    border-bottom: 2px solid #f0f0f0;
    margin-bottom: 30px;
}

.nav-tab {
    padding: 15px 25px;
    border: none;
    background: none;
    color: #666;
    font-size: 1rem;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.nav-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.nav-tab i {
    margin-right: 8px;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496, #0f6674);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* 功能区域样式 */
.function-area {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.control-group {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.control-group:last-child {
    margin-bottom: 0;
}

/* 表单控件样式 */
.file-upload {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-name {
    color: #666;
    font-style: italic;
}

.select-group, .input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    flex: 1;
    min-width: 200px;
}

.select-group label, .input-group label {
    font-weight: 500;
    color: #555;
}

.select-input, .text-input, .number-input {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.select-input:focus, .text-input:focus, .number-input:focus {
    outline: none;
    border-color: #667eea;
}

.number-input {
    width: 100px;
}

/* 进度条样式 */
.progress-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background: #e9ecef;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
    width: 0%;
}

/* 单独进度显示样式 */
.single-progress-container {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    border: 1px solid #e9ecef;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.progress-text {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
}

.progress-time {
    font-size: 0.85rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.progress-time.timeout-warning {
    color: #fd7e14;
}

.progress-time.timeout-error {
    color: #dc3545;
    font-weight: bold;
}

/* 按钮加载状态 */
.btn.loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.btn.loading .fas {
    animation: spin 1s linear infinite;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #666;
}

/* 单个分镜进度显示 */
.single-scene-progress {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    margin-bottom: 15px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-title {
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
}

.progress-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.image-progress-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.image-progress-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.image-progress-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #495057;
}

.image-progress-status {
    font-size: 0.85rem;
    color: #6c757d;
}

.image-progress-status.generating {
    color: #007bff;
}

.image-progress-status.success {
    color: #28a745;
}

.image-progress-status.failed {
    color: #dc3545;
}

.image-progress-time {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
    text-align: right;
}

.image-progress-time.warning {
    color: #fd7e14;
}

.image-progress-time.error {
    color: #dc3545;
    font-weight: bold;
}

/* 批量进度显示 */
.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-header h4 {
    margin: 0;
    color: #495057;
    font-size: 1.1rem;
}

.batch-progress-details {
    margin-top: 20px;
    display: grid;
    gap: 10px;
    max-height: 400px;
    overflow-y: auto;
}

.batch-scene-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.batch-scene-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.batch-scene-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #495057;
}

.batch-scene-status {
    font-size: 0.85rem;
    color: #6c757d;
}

.batch-scene-status.waiting {
    color: #6c757d;
}

.batch-scene-status.generating {
    color: #007bff;
}

.batch-scene-status.success {
    color: #28a745;
}

.batch-scene-status.failed {
    color: #dc3545;
}

.batch-scene-progress {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.batch-scene-time {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.batch-scene-images {
    font-size: 0.8rem;
    color: #495057;
}

/* 保存设置样式 */
.save-settings-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.setting-group {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.setting-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.setting-description {
    display: block;
    color: #6c757d;
    font-size: 0.85rem;
    margin-top: 5px;
    line-height: 1.4;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #495057;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.setting-preview {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
}

.filename-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px 12px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #495057;
    margin-top: 8px;
}

/* 上传历史记录样式 */
.upload-history-section {
    margin-top: 30px;
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
}

.upload-history-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
    max-height: 600px;
    overflow-y: auto;
}

.history-loading,
.history-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.history-empty i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.history-list {
    display: grid;
    gap: 15px;
}

.history-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.history-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.history-thumbnail {
    flex-shrink: 0;
    width: 80px;
    height: 142px; /* 80 * 16/9 = 142.22，保持9:16比例 */
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.history-thumbnail:hover {
    transform: scale(1.05);
}

.history-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.history-info {
    flex: 1;
    min-width: 0;
}

.history-filename {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    word-break: break-all;
}

.history-details {
    display: flex;
    flex-direction: column;
    gap: 3px;
    font-size: 0.85rem;
    color: #6c757d;
}

.history-url {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    word-break: break-all;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.history-url:hover {
    background: #e9ecef;
}

.history-actions {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.history-actions .btn {
    min-width: 80px;
    font-size: 0.85rem;
    padding: 6px 12px;
}

.btn-sm {
    font-size: 0.8rem;
    padding: 4px 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .history-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .history-thumbnail {
        width: 100%;
        height: 178px; /* 100 * 16/9 = 177.78，保持9:16比例 */
    }

    .history-actions {
        flex-direction: row;
        width: 100%;
        justify-content: space-between;
    }
}

/* 分镜容器样式 */
.scenes-container {
    display: grid;
    gap: 20px;
}

.scene-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
    width: 100% !important; /* 确保使用全部宽度 */
    box-sizing: border-box !important; /* 包含padding和border */
}

.scene-card:hover {
    border-color: #667eea;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.scene-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.scene-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.scene-checkbox {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.scene-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 针对一键生成故事的网格布局 - 优化版本 */
.story-scene-content {
    display: grid;
    grid-template-columns: 2fr 1fr; /* 左侧占2/3，右侧占1/3 */
    gap: 20px;
    align-items: start;
}

/* 左侧区域：文生图prompt和参考图说明 */
.scene-prompt {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* 文生图prompt文本框 */
.scene-prompt > textarea {
    min-height: 150px;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
    transition: border-color 0.3s ease;
    line-height: 1.5;
}

.scene-prompt > textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 角色形象生成模块布局优化 */
.prompt-section {
    margin-bottom: 20px;
}

.prompt-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

/* 参考图说明框样式 - 优化版本 */
.reference-text-container {
    margin: 0; /* 移除外边距，由父容器的gap控制间距 */
}

.reference-text-container label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.image-section {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 2px solid #f1f3f4;
}

.image-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

/* 角色形象生成卡片整体样式优化 */
.scene-card .scene-content {
    background: #fafbfc;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.reference-text {
    width: 100%;
    min-height: 100px; /* 增加高度与prompt保持协调 */
    padding: 12px; /* 与prompt保持一致的padding */
    border: 2px solid #e9ecef;
    border-radius: 8px; /* 与prompt保持一致的圆角 */
    font-family: inherit;
    font-size: 0.9rem; /* 与prompt保持一致的字体大小 */
    resize: vertical;
    transition: border-color 0.3s ease;
    background-color: #f8f9fa;
    color: #495057;
    line-height: 1.5; /* 与prompt保持一致的行高 */
}

.reference-text:focus {
    outline: none;
    border-color: #667eea;
    background-color: #fff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1); /* 与prompt保持一致的阴影效果 */
}

.reference-text::placeholder {
    color: #6c757d;
    font-style: italic;
}

/* 右侧区域：角色选择和操作按钮的容器 */
.scene-right-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: fit-content;
}

.scene-characters {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.character-selection {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafbfc;
    color: #495057;
    font-size: 0.9rem;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.character-selection:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.08);
    transform: translateY(-1px);
}

.character-selection i {
    font-size: 1.2rem;
    color: #667eea;
}

.selected-characters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.character-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;
}

.character-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.character-tag .remove {
    cursor: pointer;
    font-weight: bold;
    padding: 2px 4px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transition: background 0.3s ease;
}

.character-tag .remove:hover {
    background: rgba(255, 255, 255, 0.3);
}

.scene-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.scene-actions .input-group {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
}

.scene-actions .input-group label {
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
}

.image-count-input {
    width: 70px;
    padding: 8px;
    border: 2px solid #ddd;
    border-radius: 6px;
    text-align: center;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.image-count-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 优化生成按钮样式 */
.scene-actions .btn {
    padding: 10px 16px;
    font-size: 0.9rem;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-height: 40px;
}

.scene-actions .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.scene-actions .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.generated-images {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 2px solid #e9ecef;
    width: 100% !important; /* 确保使用全部宽度 */
    overflow: visible !important; /* 确保内容不被裁剪 */
}

.images-grid {
    display: grid !important;
    grid-template-columns: repeat(10, 100px) !important; /* 固定10列，每列100px */
    gap: 10px !important;
    margin-top: 10px !important;
    width: 100% !important;
    justify-content: start !important;
}

.image-thumb {
    width: 100px !important; /* 减小宽度，让更多图片能够横向排列 */
    height: 178px !important; /* 100 * 16/9 = 177.78，保持9:16比例 */
    object-fit: cover !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    border: 2px solid #ddd !important;
    transition: all 0.3s ease !important;
    flex-shrink: 0 !important; /* 防止在flex布局中被压缩 */
    display: block !important; /* 确保是块级元素 */
}

.image-thumb:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

/* 超级强制横向布局类 - 使用固定列Grid布局 */
.force-horizontal-grid {
    display: grid !important;
    grid-template-columns: repeat(10, 100px) !important; /* 固定10列 */
    gap: 10px !important;
    margin: 10px 0 !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: none !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
    overflow: visible !important;
    clear: both !important;
    float: none !important;
    position: static !important;
    z-index: auto !important;
    justify-content: start !important;
}

.force-horizontal-grid > * {
    display: block !important;
    float: none !important;
    position: static !important;
    clear: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

.force-horizontal-grid .image-thumb,
.force-horizontal-grid > img {
    width: 100px !important;
    height: 178px !important;
    object-fit: cover !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    border: 2px solid #ddd !important;
    transition: all 0.3s ease !important;
    display: block !important;
    float: none !important;
    position: static !important;
    clear: none !important;
    margin: 0 !important;
    padding: 0 !important;
    vertical-align: top !important;
    box-sizing: border-box !important;
}

/* 超级强制Grid布局 - 桌面端专用 */
.super-force-grid {
    display: grid !important;
    grid-template-columns: repeat(10, 100px) !important; /* 固定10列，足够容纳大部分情况 */
    gap: 10px !important;
    width: 100% !important;
    justify-content: start !important;
    margin: 10px 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    overflow: visible !important;
    clear: both !important;
    float: none !important;
    position: static !important;
}

.super-force-grid .image-thumb,
.super-force-grid > img {
    width: 100px !important;
    height: 178px !important;
    object-fit: cover !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    border: 2px solid #ddd !important;
    transition: all 0.3s ease !important;
    display: block !important;
    float: none !important;
    position: static !important;
    clear: none !important;
    margin: 0 !important;
    padding: 0 !important;
    vertical-align: top !important;
    box-sizing: border-box !important;
}





/* 配置页面样式 */
.config-section {
    background: white;
    border-radius: 8px;
    padding: 25px;
}

/* 角色配置区域头部样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: none;
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
}

.section-header h3 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 添加角色按钮现代化样式 */
#addCharacterBtn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

#addCharacterBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#addCharacterBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

#addCharacterBtn:hover::before {
    left: 100%;
}

#addCharacterBtn:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

#addCharacterBtn i {
    margin-right: 8px;
    font-size: 1rem;
}

.config-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.config-item:hover {
    border-color: #667eea;
}

.config-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.config-item-title {
    font-weight: 600;
    font-size: 1.1rem;
    color: #2c3e50;
}

/* 角色卡片操作按钮 */
.character-card .config-item-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.character-card .config-item-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-width: 40px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.character-card .config-item-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.character-card .config-item-actions .btn i {
    font-size: 0.9rem;
}

/* 编辑按钮特殊样式 */
.character-card .config-item-actions .btn[onclick*="editCharacter"]:hover {
    background: rgba(40, 167, 69, 0.9);
    border-color: #28a745;
}

/* 删除按钮特殊样式 */
.character-card .config-item-actions .btn[onclick*="deleteCharacter"]:hover {
    background: rgba(220, 53, 69, 0.9);
    border-color: #dc3545;
}

/* 通用操作按钮样式 */
.config-item-actions {
    display: flex;
    gap: 10px;
}

.config-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

/* 日志面板样式 */
.log-panel {
    position: fixed;
    right: -400px;
    top: 0;
    width: 400px;
    height: 100vh;
    background: white;
    border-left: 2px solid #e9ecef;
    box-shadow: -5px 0 15px rgba(0,0,0,0.1);
    transition: right 0.3s ease;
    z-index: 1000;
}

.log-panel.show {
    right: 0;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 2px solid #f0f0f0;
    background: #f8f9fa;
}

.log-header-controls {
    display: flex;
    gap: 10px;
}

.log-content {
    height: calc(100vh - 80px);
    overflow-y: auto;
    padding: 15px;
}

.log-entry {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    font-size: 0.85rem;
    border-left: 4px solid #ddd;
}

.log-entry.info {
    background: #e8f4fd;
    border-left-color: #17a2b8;
}

.log-entry.error {
    background: #f8e6e6;
    border-left-color: #dc3545;
}

.log-entry.warning {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.log-timestamp {
    font-size: 0.75rem;
    color: #666;
    margin-bottom: 5px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.modal-content {
    background: white;
    border-radius: 10px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.image-modal-content {
    max-width: 90vw;
    max-height: 90vh;
    overflow: visible;
}

/* 优化模型服务编辑表单 */
.models-section {
    margin-top: 20px;
    border-top: 2px solid #e9ecef;
    padding-top: 20px;
}

.models-list {
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
    background: #f8f9fa;
    min-height: 60px;
    max-height: 400px;
    overflow-y: auto;
}

/* 只有当内容超出时才显示滚动条 */
.models-list:not(:hover) {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE */
}

.models-list:not(:hover)::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
}

.models-list:hover {
    scrollbar-width: thin;
    -ms-overflow-style: auto;
}

.models-list:hover::-webkit-scrollbar {
    display: block;
    width: 6px;
}

.models-list:hover::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.models-list:hover::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 3px;
}

.models-list:hover::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

.model-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px;
    background: white;
    border-radius: 5px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.model-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.model-item:last-child {
    margin-bottom: 0;
}

.model-name {
    flex: 1;
}

.model-type {
    width: 120px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.modal-header h4 {
    color: #2c3e50;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 5px;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

/* 图片模态框的特殊样式 */
.image-modal-content .modal-body {
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 2px solid #f0f0f0;
}



/* 角色表单现代化样式 */
.character-form .config-form {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.character-form .input-group {
    margin-bottom: 24px;
    position: relative;
}

.character-form .input-group label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    display: block;
    font-size: 0.95rem;
}

.character-form .input-group label span[style*="color: red"] {
    color: #e74c3c !important;
    font-weight: 700;
}

.character-form .text-input,
.character-form .select-input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.character-form .text-input:focus,
.character-form .select-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.character-form textarea.text-input {
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
    line-height: 1.5;
}

.character-form .input-group small {
    color: #6c757d;
    font-size: 0.85rem;
    margin-top: 6px;
    display: block;
    line-height: 1.4;
}

/* 表单网格布局优化 */
.character-form .config-form > div[style*="grid-template-columns"] {
    gap: 24px !important;
    margin-bottom: 24px !important;
}

/* 表单注意事项样式 */
.form-note {
    margin-top: 20px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 12px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-note small {
    color: #495057;
    line-height: 1.5;
    font-size: 0.9rem;
}

/* 表单输入框图标 */
.character-form .input-group.with-icon {
    position: relative;
}

.character-form .input-group.with-icon::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-size: contain;
    z-index: 2;
}

.character-form .input-group.with-icon .text-input {
    padding-left: 44px;
}

/* 角色容器空状态样式 */
.no-data {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 60px 40px;
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    border-radius: 16px;
    border: 2px dashed #dee2e6;
    position: relative;
    overflow: hidden;
}

.no-data::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.05), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.no-data i {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 16px;
    display: block;
}

.no-data::after {
    content: '点击上方"添加角色"按钮开始配置';
    display: block;
    margin-top: 12px;
    font-size: 0.9rem;
    color: #adb5bd;
    font-style: normal;
}

/* 角色选择网格 */
.character-selection-grid .character-option {
    transition: all 0.3s ease;
}

.character-selection-grid .character-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.character-selection-grid .character-option.selected {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

/* 配置项样式增强 */
.config-details {
    margin-top: 10px;
}

.config-details p {
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.config-details img {
    margin-bottom: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.config-details img:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

/* 图片预览模态框样式增强 */
.image-modal-content {
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.image-preview-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 80vh;
    min-height: 400px;
    padding: 20px;
    overflow: auto;
    background: #f8f9fa;
    border-radius: 8px;
    box-sizing: border-box;
}

/* 图床功能样式 */
.imagehost-config {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: #555;
    margin-bottom: 5px;
}

.form-input {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
}

.form-note {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 0.8rem;
    line-height: 1.4;
}

.upload-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.upload-area {
    border: 3px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.upload-area.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
}

.upload-hint {
    color: #666;
    font-size: 0.9rem;
    margin-top: 10px;
}

.upload-progress {
    margin-bottom: 20px;
}

.upload-result {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.result-content {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.result-links {
    flex: 1;
}

.link-group {
    margin-bottom: 15px;
}

.link-group label {
    font-weight: 500;
    color: #555;
    margin-bottom: 5px;
}

.link-input-group {
    display: flex;
    gap: 10px;
}

.link-input-group .form-input {
    flex: 1;
    background: #fff;
}

/* 角色形象生成模块样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    font-size: 1.1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #ddd;
}

.character-checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.character-checkbox {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.character-checkbox:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.character-checkbox input[type="checkbox"] {
    margin: 0;
}

.character-name {
    font-size: 0.9rem;
    color: #555;
}

.no-characters {
    color: #999;
    font-style: italic;
    font-size: 0.9rem;
}

.image-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.image-count {
    color: #666;
    font-size: 0.9rem;
}

.generated-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.generated-image {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.generated-image:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.generated-image img {
    width: 100%;
    height: 267px; /* 150 * 16/9 = 266.67，保持9:16比例 */
    object-fit: cover;
    cursor: pointer;
}

/* 这个样式已移动到更具体的选择器中 */

/* 角色形象生成专用样式 */
.chargen-prompt {
    width: 100%;
    min-height: 120px;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.chargen-prompt:focus {
    outline: none;
    border-color: #667eea;
}

.chargen-reference-text {
    width: 100%;
    min-height: 120px;
    max-height: 200px;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
    transition: border-color 0.3s ease;
    background-color: #f8f9fa;
    color: #495057;
}

.chargen-reference-text:focus {
    outline: none;
    border-color: #667eea;
    background-color: #fff;
}

.image-count-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.image-count-control label {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

.number-input-sm {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    text-align: center;
}

.number-input-sm:focus {
    outline: none;
    border-color: #667eea;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.8rem;
    line-height: 1.2;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.input-group label {
    margin: 0;
    white-space: nowrap;
}

/* 角色形象生成图片导航样式 */
.images-wrapper {
    position: relative;
}

.images-wrapper .generated-image {
    display: none;
}

.images-wrapper .generated-image.active {
    display: block;
}

.image-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.nav-btn {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
}

.nav-btn:hover:not(:disabled) {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-1px);
}

.nav-btn:disabled {
    background: #f8f9fa;
    color: #ccc;
    cursor: not-allowed;
    border-color: #e9ecef;
}

.image-counter {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
}

/* 角色形象生成模块样式优化 */
.character-selection-section {
    margin: 15px 0;
}

.character-selection-section label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.character-selection-container {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    background: #f8f9fa;
}

/* 横向滚动角色选择 */
.chargen-character-scroll {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    padding: 5px 0 10px 0;
    scrollbar-width: thin;
    scrollbar-color: #667eea #f1f1f1;
}

.chargen-character-scroll::-webkit-scrollbar {
    height: 6px;
}

.chargen-character-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chargen-character-scroll::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 3px;
}

.chargen-character-scroll::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

.chargen-character-card {
    flex: 0 0 auto;
    width: 120px;
    border: 2px solid #ddd;
    border-radius: 12px;
    padding: 12px;
    cursor: pointer;
    text-align: center;
    background: white;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chargen-character-card:hover {
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.2);
}

.chargen-character-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(102, 126, 234, 0.05));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);
}

.character-thumbnail {
    margin-bottom: 8px;
}

.character-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 5px;
    border: 1px solid #eee;
}

.character-placeholder {
    width: 50px;
    height: 50px;
    background: #f0f0f0;
    border-radius: 5px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 1.2rem;
}

.character-info .character-name {
    font-weight: bold;
    margin-bottom: 2px;
    font-size: 0.9rem;
    color: #333;
}

.character-info .character-code {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 5px;
}

.character-selected {
    color: #667eea;
    font-size: 0.8rem;
    font-weight: 500;
}

.character-selection-hint {
    margin-top: 12px;
    padding: 10px 15px;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 8px;
    border: 1px solid #e9ecef;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.character-selection-hint small {
    color: #666;
    line-height: 1.4;
}

/* 新的角色卡片样式 */
.character-avatar {
    width: 70px;
    height: 70px;
    margin: 0 auto 8px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    position: relative;
    transition: all 0.3s ease;
}

.character-avatar .character-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.character-avatar .character-placeholder {
    color: #adb5bd;
    font-size: 1.8rem;
}

.selection-indicator {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 24px;
    height: 24px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.7rem;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.character-details {
    text-align: center;
}

.character-details .character-name {
    font-weight: 600;
    color: #333;
    font-size: 0.85rem;
    margin-bottom: 3px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.character-details .character-code {
    color: #666;
    font-size: 0.75rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.no-characters-message {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 2px dashed #ddd;
}

.no-characters-message i {
    color: #667eea;
    margin-right: 8px;
}

/* 角色形象生成图片网格样式 */
.chargen-images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.chargen-image-item {
    position: relative;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.chargen-image-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.chargen-image-item img {
    width: 100%;
    height: 356px; /* 200 * 16/9 = 355.56，保持9:16比例 */
    object-fit: cover;
    display: block;
}

.chargen-image-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 15px 10px 8px;
    text-align: center;
}

.chargen-image-info span {
    display: block;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.chargen-image-info small {
    font-size: 0.75rem;
    opacity: 0.9;
    color: #4ade80;
}

/* 图片导航按钮 */
.image-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-nav-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-50%) scale(1.1);
}

.image-nav-btn:disabled {
    background: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
    transform: translateY(-50%);
}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

/* 模态框中的图片信息显示 */
.image-preview-container .image-info {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9rem;
    z-index: 10;
    pointer-events: none; /* 防止阻挡点击 */
}

/* 角色形象生成图片项中的信息显示 */
.chargen-image-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 15px 10px 8px;
    text-align: center;
}

#previewImage {
    max-width: calc(100% - 40px);
    max-height: calc(100% - 40px);
    width: auto;
    height: auto;
    border-radius: 8px;
    transition: transform 0.3s ease;
    cursor: zoom-in;
    display: block;
    margin: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    object-fit: contain;
    flex-shrink: 0;
}

#previewImage.zoomed {
    cursor: zoom-out;
}

/* 确保图片在缩放时保持居中 */
.image-preview-container {
    overflow: auto;
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;
}

.image-preview-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.image-preview-container::-webkit-scrollbar-track {
    background: transparent;
}

.image-preview-container::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.image-preview-container::-webkit-scrollbar-thumb:hover {
    background: #999;
}

/* 响应式图片居中 */
@media (max-width: 768px) {
    .image-preview-container {
        height: 70vh;
        min-height: 300px;
        padding: 10px;
    }

    #previewImage {
        max-width: calc(100% - 20px);
        max-height: calc(100% - 20px);
    }
}

/* 确保图片在所有情况下都居中 */
.image-preview-container > #previewImage {
    position: relative;
    z-index: 1;
}

/* 图片缩放时的居中处理 */
#previewImage.zoomed {
    transform-origin: center center;
}

.image-controls {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
    border-top: 2px solid #e9ecef;
    justify-content: center;
    flex-wrap: wrap;
}

.zoom-controls {
    display: flex;
    gap: 5px;
    align-items: center;
}

/* 角色卡片现代化样式 */
.character-card {
    position: relative;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 16px;
    padding: 0;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: move;
    overflow: hidden;
    margin-bottom: 20px;
}

.character-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
}

.character-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.character-card:hover::before {
    opacity: 1;
}

/* 角色卡片头部 */
.character-card .config-item-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    margin: 0 0 20px 0;
    border-radius: 0;
    position: relative;
}

.character-card .config-item-header::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #764ba2;
}

.character-card .config-item-title {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 角色卡片内容区域 */
.character-card .config-details {
    padding: 0 20px 20px 20px;
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 20px;
    align-items: start;
}

/* 角色缩略图样式优化 */
.character-thumbnail {
    width: 100px;
    height: 178px; /* 100 * 16/9 = 177.78，保持9:16比例 */
    object-fit: cover;
    border-radius: 12px;
    border: 3px solid #e9ecef;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.character-thumbnail:hover {
    border-color: #667eea;
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* 无图片占位符 */
.no-image-placeholder {
    width: 100px !important;
    height: 178px !important; /* 100 * 16/9 = 177.78，保持9:16比例 */
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #adb5bd !important;
    border: 3px solid #e9ecef !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
}

.no-image-placeholder:hover {
    border-color: #667eea !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    transform: scale(1.05) !important;
}

/* 角色信息区域 */
.character-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.character-info p {
    margin: 0;
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    line-height: 1.4;
}

.character-info strong {
    color: #495057;
    font-weight: 600;
    min-width: 80px;
    margin-right: 8px;
}

.character-info .info-value {
    color: #667eea;
    font-weight: 500;
    background: rgba(102, 126, 234, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
}

.character-info .core-features {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.85rem;
    line-height: 1.4;
    border-left: 3px solid #28a745;
}

/* 角色容器网格布局 */
#charactersContainer {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    padding: 20px 0;
}

/* 角色卡片动画效果 */
.character-card {
    animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 角色卡片拖拽状态 */
.character-card.dragging {
    opacity: 0.7;
    transform: rotate(3deg) scale(1.05);
    z-index: 1000;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* 角色卡片放置目标 */
.character-card.drag-over {
    border: 2px dashed #667eea;
    background: rgba(102, 126, 234, 0.05);
}

/* 角色图片容器 */
.character-image-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 角色卡片加载状态 */
.character-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.character-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
}

/* 角色卡片成功状态动画 */
.character-card.success {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(40, 167, 69, 0.3); }
    100% { transform: scale(1); }
}

/* 角色卡片错误状态动画 */
.character-card.error {
    animation: errorShake 0.6s ease-out;
    border-color: #dc3545 !important;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 模态框中的角色表单特殊样式 */
.modal-content .character-form {
    max-height: 70vh;
    overflow-y: auto;
}

.modal-content .character-form::-webkit-scrollbar {
    width: 6px;
}

.modal-content .character-form::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.modal-content .character-form::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 3px;
}

.modal-content .character-form::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

.character-card:hover {
    border-color: #667eea;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}



/* 拖拽手柄现代化样式 */
.character-card .drag-handle {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.2rem;
    cursor: grab;
    transition: all 0.3s ease;
    z-index: 10;
    padding: 5px;
    border-radius: 4px;
}

.character-card .drag-handle:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-50%) scale(1.1);
}

.character-card .drag-handle:active {
    cursor: grabbing;
    transform: translateY(-50%) scale(0.95);
}

/* 通用拖拽手柄（其他地方使用） */
.drag-handle {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #999;
    cursor: move;
    font-size: 16px;
    transition: all 0.3s ease;
}

.drag-handle:hover {
    color: #667eea;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    /* 角色容器在中等屏幕上的调整 */
    #charactersContainer {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
    }

    .character-card .config-details {
        grid-template-columns: 1fr;
        gap: 16px;
        text-align: center;
    }

    .character-image-container {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }

    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .control-group {
        flex-direction: column;
    }

    .story-scene-content {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .scene-right-panel {
        gap: 15px;
    }

    .scene-actions {
        padding: 12px;
    }

    .scene-actions .btn {
        padding: 12px 16px;
        font-size: 1rem;
    }



    /* 角色配置移动端优化 */
    #charactersContainer {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 16px 0;
    }

    .character-card {
        margin-bottom: 16px;
    }

    .character-card .config-item-header {
        padding: 16px;
        font-size: 1rem;
    }

    .character-card .config-item-title {
        font-size: 1.1rem;
    }

    .character-card .config-details {
        padding: 0 16px 16px 16px;
        grid-template-columns: 1fr;
        gap: 12px;
        text-align: center;
    }

    .character-thumbnail,
    .no-image-placeholder {
        width: 80px !important;
        height: 142px !important; /* 80 * 16/9 = 142.22，保持9:16比例 */
    }

    .character-card .config-item-actions .btn {
        padding: 6px 10px;
        font-size: 0.8rem;
        min-width: 36px;
        height: 32px;
    }

    .character-card .drag-handle {
        right: 12px;
        font-size: 1rem;
    }

    /* 表单移动端优化 */
    .character-form .config-form {
        padding: 20px;
    }

    .character-form .config-form > div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
    }

    .section-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
        padding: 16px 0;
    }

    .section-header h3 {
        font-size: 1.5rem;
    }

    #addCharacterBtn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .chargen-character-scroll {
        gap: 10px;
    }

    .chargen-character-card {
        width: 100px;
        padding: 8px;
    }

    .character-avatar {
        width: 60px;
        height: 60px;
    }

    .chargen-images-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }

    .chargen-image-item img {
        height: 267px; /* 150 * 16/9 = 266.67，保持9:16比例 */
    }

    .log-panel {
        width: 100%;
        right: -100%;
    }

    .config-form {
        grid-template-columns: 1fr;
    }

    /* 模型表单响应式 */
    .config-form [style*="grid-template-columns: 1fr 1fr"] {
        grid-template-columns: 1fr !important;
    }

    .config-form [style*="grid-template-columns: 1fr 1fr 1fr"] {
        grid-template-columns: 1fr !important;
    }

    .model-item {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .model-item .model-name {
        width: 100% !important;
    }

    .model-item .model-type {
        width: 100% !important;
    }
}

@media (max-width: 480px) {
    /* 小屏幕进一步优化 */
    .character-card .config-item-header {
        padding: 12px;
    }

    .character-card .config-details {
        padding: 0 12px 12px 12px;
    }

    .character-thumbnail,
    .no-image-placeholder {
        width: 60px !important;
        height: 107px !important; /* 60 * 16/9 = 106.67，保持9:16比例 */
    }

    .character-info p {
        font-size: 0.85rem;
    }

    .character-form .config-form {
        padding: 16px;
    }

    .form-note {
        padding: 12px 16px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Excel模块特定样式 */
.excel-config {
    margin-bottom: 30px;
}

.excel-config .form-group {
    margin-bottom: 20px;
}

.excel-config .form-note {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
}

.success-message {
    color: #28a745;
    padding: 15px;
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: 5px;
    margin-bottom: 15px;
}

.success-message i {
    margin-right: 8px;
}

.error-message {
    color: #dc3545;
    padding: 15px;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 5px;
    margin-bottom: 15px;
}

.error-message i {
    margin-right: 8px;
}

.result-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.result-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* TXT上传区域特定样式 */
#txtUploadArea .upload-icon {
    font-size: 3rem;
    color: #28a745;
    margin-bottom: 15px;
}

#txtUploadArea .upload-hint {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Excel模块响应式 */
@media (max-width: 768px) {
    .result-actions {
        flex-direction: column;
    }

    .result-actions .btn {
        width: 100%;
        min-width: auto;
    }
}

/* ===== 角色分组和服装管理样式 ===== */

.character-group {
    margin-bottom: 24px;
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.character-group:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.character-group .config-item-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 24px;
    border-bottom: none;
    position: relative;
}

.character-group .config-item-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.character-group .config-item-title {
    font-size: 1rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    z-index: 1;
}

.character-group .config-item-title i {
    color: #ffffff;
    font-size: 1.1rem;
    opacity: 0.9;
}

.outfit-count {
    background: rgba(255,255,255,0.25);
    color: #ffffff;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 12px;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.character-details {
    padding: 24px;
    background: #fafbfc;
}

.character-info-section {
    margin-bottom: 24px;
}

.character-basic-info p {
    margin-bottom: 12px;
    color: #6c757d;
    font-size: 0.95rem;
}

.character-basic-info strong {
    color: #495057;
    font-weight: 600;
}

.core-features {
    margin-top: 16px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    border-radius: 12px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.core-features strong {
    color: #667eea;
    font-weight: 600;
    font-size: 0.95rem;
}

.core-features-text {
    margin-top: 12px;
    color: #495057;
    line-height: 1.7;
    white-space: pre-wrap;
    font-size: 0.9rem;
}

.outfits-section {
    border-top: 1px solid #e9ecef;
    padding-top: 24px;
    background: #ffffff;
    margin: 0 -24px -24px -24px;
    padding: 24px;
    border-radius: 0 0 16px 16px;
}

.outfits-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.outfits-header h4 {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.outfits-header h4::before {
    content: '👔';
    font-size: 1.2rem;
}

.outfits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
}

.outfit-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    background: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.outfit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.outfit-card:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    transform: translateY(-4px);
    border-color: #667eea;
}

.outfit-card:hover::before {
    opacity: 1;
}

.outfit-image {
    height: 356px; /* 200 * 16/9 = 355.56，保持9:16比例 */
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    border-radius: 8px;
}

.outfit-image img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 6px;
}

.outfit-image:hover img {
    transform: scale(1.02);
}

/* 添加图片预览覆盖层 */
.outfit-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.6);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    font-size: 0.85rem;
    font-weight: 500;
    pointer-events: none;
}

.outfit-image:hover::after {
    opacity: 1;
}

.outfit-image::before {
    content: '\f00e';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 1.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.outfit-image:hover::before {
    opacity: 1;
}

.outfit-image .no-image-placeholder {
    color: #adb5bd;
    text-align: center;
    padding: 20px;
}

.outfit-image .no-image-placeholder i {
    font-size: 2.5rem;
    margin-bottom: 8px;
    display: block;
    opacity: 0.6;
}

.outfit-image .no-image-placeholder div {
    font-size: 0.85rem;
    font-weight: 500;
}

.outfit-info {
    padding: 16px;
    position: relative;
    z-index: 1;
}

.outfit-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 6px;
    font-size: 0.95rem;
    line-height: 1.3;
}

.outfit-description {
    font-size: 0.85rem;
    color: #6c757d;
    line-height: 1.5;
    margin-bottom: 0;
}

.outfit-actions {
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    border-top: 1px solid #e9ecef;
}

.outfit-actions .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.outfit-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.outfit-actions .btn i {
    font-size: 0.75rem;
}

.no-outfits {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
    grid-column: 1 / -1;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.no-outfits i {
    font-size: 4rem;
    margin-bottom: 20px;
    display: block;
    color: #adb5bd;
    opacity: 0.7;
}

.no-outfits p {
    margin-bottom: 20px;
    font-size: 1.1rem;
    font-weight: 500;
    color: #495057;
}

.no-outfits .btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.no-outfits .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

/* 按钮样式增强 */
.btn-xs {
    padding: 6px 12px;
    font-size: 0.75rem;
    border-radius: 6px;
    font-weight: 500;
}

.config-item-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.config-item-actions .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.config-item-actions .btn-success {
    background: rgba(40, 167, 69, 0.9);
    color: white;
    border-color: rgba(255,255,255,0.3);
}

.config-item-actions .btn-success:hover {
    background: rgba(33, 136, 56, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.config-item-actions .btn:not(.btn-success):not(.btn-danger) {
    background: rgba(255,255,255,0.2);
    color: white;
}

.config-item-actions .btn:not(.btn-success):not(.btn-danger):hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

.config-item-actions .btn-danger {
    background: rgba(220, 53, 69, 0.9);
    color: white;
    border-color: rgba(255,255,255,0.3);
}

.config-item-actions .btn-danger:hover {
    background: rgba(200, 35, 51, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .outfits-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }

    .character-group .config-item-header {
        padding: 15px;
    }

    .character-details {
        padding: 15px;
    }

    .config-item-actions {
        flex-direction: column;
        gap: 8px;
    }

    .config-item-actions .btn {
        margin-left: 0;
        width: 100%;
    }
}

/* ===== 现代化表单样式 ===== */

.modern-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-header {
    text-align: center;
    margin-bottom: 32px;
    padding: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px 16px 0 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.form-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.form-icon {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.3);
}

.form-icon i {
    font-size: 1.5rem;
    color: white;
}

.form-header h3 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.form-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 0.95rem;
    font-weight: 400;
}

.form-body {
    padding: 32px;
    background: white;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 24px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.form-label i {
    color: #667eea;
    font-size: 0.9rem;
}

.required {
    color: #e74c3c;
    font-weight: 700;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #ffffff;
    box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: #fafbff;
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
    line-height: 1.5;
}

.form-help {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 6px;
    font-size: 0.85rem;
    color: #6c757d;
}

.form-help i {
    color: #17a2b8;
    font-size: 0.8rem;
}

.image-preview-container {
    position: relative;
    display: inline-block;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-preview-container:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.preview-image {
    max-width: 200px;
    max-height: 356px; /* 200 * 16/9 = 355.56，保持9:16比例 */
    width: auto;
    height: auto;
    display: block;
    border-radius: 12px;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    font-size: 0.85rem;
    font-weight: 500;
}

.image-preview-container:hover .preview-overlay {
    opacity: 1;
}

.preview-overlay i {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

/* 表单行布局 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 24px;
}

/* 选择框样式 */
.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #ffffff;
    box-sizing: border-box;
    cursor: pointer;
}

.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: #fafbff;
}

/* 角色编码验证样式 */
.code-validation-message {
    margin-top: 6px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.code-validation-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.code-validation-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.code-validation-message.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.form-input.valid {
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.1);
}

.form-input.invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
}

.form-input.checking {
    border-color: #ffc107;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.1);
}

/* 提示区域样式 */
.form-tips {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    border: 1px solid #e1e8ff;
    border-radius: 12px;
    padding: 20px;
    margin-top: 24px;
}

.tips-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: #667eea;
    font-weight: 600;
}

.tips-header i {
    color: #ffc107;
    font-size: 1rem;
}

.tips-list {
    margin: 0;
    padding-left: 20px;
    color: #495057;
}

.tips-list li {
    margin-bottom: 6px;
    line-height: 1.5;
}

.tips-list li:last-child {
    margin-bottom: 0;
}

/* 图片URL输入组 */
.image-url-input-group {
    display: flex;
    gap: 12px;
    align-items: stretch;
}

.image-url-input-group .form-input {
    flex: 1;
    margin-bottom: 0;
}

.image-select-btn {
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
}

.image-select-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.image-select-btn i {
    font-size: 0.85rem;
}

/* 图片选择器模态框 */
.image-selector-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10001;
}

.image-selector-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 800px;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.image-selector-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.image-selector-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.image-selector-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.image-selector-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.image-selector-body {
    padding: 24px;
    max-height: 500px;
    overflow-y: auto;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
}

.image-grid-item {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.image-grid-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-grid-item.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.image-grid-thumbnail {
    width: 100%;
    height: 213px; /* 120 * 16/9 = 213.33，保持9:16比例 */
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.image-grid-thumbnail img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.image-grid-info {
    padding: 8px;
    font-size: 0.75rem;
    color: #6c757d;
    text-align: center;
    border-top: 1px solid #f0f0f0;
}

.image-grid-filename {
    font-weight: 500;
    color: #495057;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.image-selector-footer {
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: #f8f9fa;
}

.image-selector-footer .btn {
    padding: 8px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.image-selector-footer .btn-primary {
    background: #667eea;
    color: white;
    border: 1px solid #667eea;
}

.image-selector-footer .btn-primary:hover {
    background: #5a6fd8;
    border-color: #5a6fd8;
}

.image-selector-footer .btn-secondary {
    background: #6c757d;
    color: white;
    border: 1px solid #6c757d;
}

.image-selector-footer .btn-secondary:hover {
    background: #5a6268;
    border-color: #5a6268;
}

.image-selector-footer .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .form-body {
        padding: 24px 20px;
    }

    .form-header {
        padding: 20px;
    }

    .modern-form {
        margin: 0 -10px;
    }

    .image-url-input-group {
        flex-direction: column;
        gap: 8px;
    }

    .image-select-btn {
        width: 100%;
        justify-content: center;
    }

    .image-selector-content {
        width: 95%;
        max-height: 90%;
    }

    .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 12px;
    }

    .image-grid-thumbnail {
        height: 178px; /* 100 * 16/9 = 177.78，保持9:16比例 */
    }
}

/* ===== 角色服装选择界面样式 ===== */

.character-outfit-selection {
    max-width: 100%;
}

.selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.selection-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.selection-count {
    font-weight: 600;
    color: #667eea;
}

.character-groups-container {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fff;
}

.character-group-selection {
    border-bottom: 1px solid #f0f0f0;
}

.character-group-selection:last-child {
    border-bottom: none;
}

.character-group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9ff;
    border-bottom: 1px solid #e8f0ff;
}

.character-group-header .character-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.character-group-header .character-info i {
    color: #667eea;
}

.character-group-header .character-name {
    font-weight: 600;
    color: #2c3e50;
}

.character-group-header .character-code {
    color: #666;
    font-size: 0.9rem;
}

.outfit-count-badge {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.outfits-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    padding: 15px 20px;
}

.outfit-selection-card {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    position: relative;
}

.outfit-selection-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.outfit-selection-card.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.outfit-image-preview {
    width: 100%;
    height: 142px; /* 80 * 16/9 = 142.22，保持9:16比例 */
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 8px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.outfit-image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.outfit-image-preview .no-image {
    color: #999;
    font-size: 1.5rem;
}

.outfit-selection-info {
    text-align: center;
}

.outfit-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.outfit-description {
    font-size: 0.8rem;
    color: #666;
    line-height: 1.3;
}

.selection-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.no-outfits-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    color: #999;
}

.no-outfits-message i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
    color: #ddd;
}

.selection-footer {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.selection-tips {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 0.9rem;
}

.selection-tips i {
    color: #ffc107;
}

/* ===== 角色形象生成模块的角色选择样式 ===== */

.chargen-character-outfit-selection {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fff;
    max-height: 300px;
    overflow-y: auto;
}

.chargen-selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9ff;
    border-bottom: 1px solid #e8f0ff;
    position: sticky;
    top: 0;
    z-index: 10;
}

.selection-label {
    font-weight: 600;
    color: #2c3e50;
}

.chargen-character-group {
    border-bottom: 1px solid #f0f0f0;
}

.chargen-character-group:last-child {
    border-bottom: none;
}

.chargen-character-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #fafbff;
}

.chargen-character-header .character-info {
    display: flex;
    align-items: center;
    gap: 6px;
}

.chargen-character-header .character-info i {
    color: #667eea;
    font-size: 0.9rem;
}

.chargen-character-header .character-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.chargen-character-header .character-code {
    color: #666;
    font-size: 0.8rem;
}

.chargen-character-header .outfit-count {
    background: #667eea;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
}

.chargen-outfits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    padding: 10px 15px;
}

.chargen-outfit-card {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;
    position: relative;
}

.chargen-outfit-card:hover {
    border-color: #667eea;
}

.chargen-outfit-card.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.outfit-image-small {
    width: 100%;
    height: 89px; /* 50 * 16/9 = 88.89，保持9:16比例 */
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 4px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.outfit-image-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.outfit-image-small .no-image-small {
    color: #999;
    font-size: 1rem;
}

.outfit-info-small {
    text-align: center;
}

.outfit-name-small {
    font-size: 0.75rem;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.2;
}

.selection-indicator-small {
    position: absolute;
    top: 2px;
    right: 2px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.no-outfits-chargen {
    grid-column: 1 / -1;
    text-align: center;
    padding: 20px;
    color: #999;
}

.no-outfits-chargen i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    display: block;
    color: #ddd;
}

.no-outfits-chargen p {
    margin: 0;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .outfits-selection-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
        padding: 10px 15px;
    }

    .chargen-outfits-grid {
        grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
        gap: 6px;
        padding: 8px 12px;
    }

    .character-group-header,
    .chargen-character-header {
        padding: 10px 15px;
    }

    .selection-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 分镜内的核心特征开关 */
.scene-core-features {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.scene-core-features label {
    font-size: 0.85rem;
    color: #495057;
    margin: 0;
}

.scene-core-features .switch {
    width: 40px;
    height: 20px;
}

.scene-core-features .slider:before {
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
}